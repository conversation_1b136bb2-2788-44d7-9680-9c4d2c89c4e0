"""
Assessment-related API routes for the quiz/assessment management system.
"""

import json
import os
from datetime import datetime
from typing import List, Optional

import psycopg2
import psycopg2.extras
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...api.middlewares.hashid_middleware import hash_ids_in_response
from ...config.db_config import DATABASE_CONFIG
from ...models.assessment_manager import (
    assessment_report_by_topic,
    assessment_report_by_user,
    assessment_report_with_question_stats,
    get_assessment_by_id,
    get_assessment_questions_by_id,
)
from ...models.db_manager import (
    count_questions_for_skills,
    insert_quiz_creation_logs,
)
from ...utils.api_response import (
    error_response,
    raise_http_exception,
    success_response,
)
from ...utils.hashid_utils import (
    decode_assessment_id,
)
from ...utils.logger import (
    error,
    info,
    warning,
)

# Create a simple rate limiter dependency
async def rate_limiter():
    """Simple rate limiter placeholder"""
    return None

# Create router for assessment-related endpoints
assessment_router = APIRouter()


# Pydantic models for assessment-related requests
class CreateQuizRequest(BaseModel):
    topic: str  # This is the user-defined description for the assessment
    quiz_name: str
    user_id: str
    skill_ids: List[int]  # Now properly used for many-to-many
    question_selection_mode: str = "dynamic"  # 'fixed' or 'dynamic'
    duration: int = 30  # Duration in minutes


class FinalQuestionsRequest(BaseModel):
    question_ids: List[int]
    quiz_name: str
    assessment_id: Optional[int] = None  # For fixed assessments


class ReportRequest(BaseModel):
    report_type: str
    user_name: Optional[str] = None
    # This refers to assessment name or part of it
    report_topic: Optional[str] = None
    # For assessment-wise reports
    assessment_base_name: Optional[str] = None
    quiz_type: Optional[str] = None


# Helper functions for assessment management
def _validate_assessment_params(question_selection_mode: str):
    """Validate assessment parameters."""
    if question_selection_mode not in ["fixed", "dynamic"]:
        raise ValueError("question_selection_mode must be either 'fixed' or 'dynamic'")


def _generate_assessment_names_and_description(
    assessment_topic_name: str, is_final: bool, description: Optional[str]
) -> tuple[str, str]:
    """Generate full assessment name and description."""
    full_assessment_name = f"{assessment_topic_name} {'Final' if is_final else 'Mock'} Assessment"
    assessment_description = description or f"{'Final' if is_final else 'Mock'} assessment for {assessment_topic_name}"
    return full_assessment_name, assessment_description


def _create_skill_associations(cur, assessment_id: int, skill_ids: List[int]):
    """Create skill associations for an assessment."""
    for skill_id in skill_ids:
        cur.execute(
            """INSERT INTO assessment_skills
            (assessment_id, skill_id)
            VALUES (%s, %s) ON CONFLICT DO NOTHING""",
            (assessment_id, skill_id),
        )


def _create_new_assessment(
    cur,
    full_assessment_name: str,
    assessment_description: str,
    is_final: bool,
    total_questions: int,
    question_selection_mode: str,
) -> int:
    """Create a new assessment in the database."""
    composition = {"basic": 6, "intermediate": 6, "advanced": 8}

    cur.execute(
        """INSERT INTO assessments
           (name, description, is_final, total_questions, question_selection_mode, composition)
           VALUES (%s, %s, %s, %s, %s, %s) RETURNING id""",
        (
            full_assessment_name,
            assessment_description,
            is_final,
            total_questions,
            question_selection_mode,
            json.dumps(composition),
        ),
    )
    return cur.fetchone()[0]


def _log_assessment_creation(
    full_assessment_name: str, assessment_description: str, total_questions: int, assessment_id: int
):
    """Log assessment creation with error handling."""
    try:
        insert_quiz_creation_logs(
            [
                {
                    "user_id": "system",  # Default user for system-created assessments
                    "assessment_name": full_assessment_name,
                    "assessment_description": assessment_description,
                    "total_questions": total_questions,
                    "assessment_id": assessment_id,
                }
            ]
        )
    except Exception as log_error:
        # Don't fail the assessment creation if logging fails
        warning(f"Failed to log assessment creation: {log_error}")


def get_or_create_assessment(
    assessment_topic_name: str,
    is_final=False,
    description: Optional[str] = None,
    skill_ids: List[int] = [],
    question_selection_mode: str = "dynamic",
):
    """
    Create assessment with multiple skills.

    Args:
        assessment_topic_name: Base name for the assessment
        is_final: Whether this is a final assessment
        description: Optional description for the assessment
        skill_ids: List of skill IDs to associate with this assessment
        question_selection_mode: How questions are selected - 'fixed' or 'dynamic'

    Returns:
        The ID of the created or existing assessment
    """
    try:
        # Validate parameters
        _validate_assessment_params(question_selection_mode)

        # Get configuration
        total_questions = int(os.getenv("TOTAL_QUESTIONS_COUNT", "30"))

        # Generate names and description
        full_assessment_name, assessment_description = _generate_assessment_names_and_description(
            assessment_topic_name, is_final, description
        )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Check if assessment exists
                cur.execute("SELECT id FROM assessments WHERE name = %s", (full_assessment_name,))
                result = cur.fetchone()

                if result:
                    assessment_id = result[0]
                    # Ensure skill associations exist
                    _create_skill_associations(cur, assessment_id, skill_ids)
                    return assessment_id

                # Create new assessment
                assessment_id = _create_new_assessment(
                    cur,
                    full_assessment_name,
                    assessment_description,
                    is_final,
                    total_questions,
                    question_selection_mode,
                )

                # Create skill associations
                _create_skill_associations(cur, assessment_id, skill_ids)

                conn.commit()

                # Log the assessment creation
                _log_assessment_creation(full_assessment_name, assessment_description, total_questions, assessment_id)

                return assessment_id

    except Exception as e:
        error(f"Error in get_or_create_assessment for '{assessment_topic_name}': {e}", exc_info=True)
        raise


def _validate_quiz_request(request: CreateQuizRequest):
    """Validate the quiz creation request parameters."""
    assessment_description = request.topic
    user_defined_assessment_name = request.quiz_name

    if not assessment_description or len(assessment_description) < 20:
        raise_http_exception(
            status_code=400,
            detail="Assessment description must be at least 20 characters long",
        )

    if not user_defined_assessment_name or len(user_defined_assessment_name) < 3:
        raise_http_exception(
            status_code=400,
            detail="Assessment name must be at least 3 characters long",
        )


def _validate_skills(skill_ids: List[int]):
    """Validate that all skill IDs exist in the database."""
    if not skill_ids:
        raise_http_exception(
            status_code=400,
            detail="At least one skill ID must be provided",
        )

    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor() as cur:
            placeholders = ",".join(["%s"] * len(skill_ids))
            cur.execute(f"SELECT id FROM skills WHERE id IN ({placeholders})", skill_ids)
            existing_skills = [row[0] for row in cur.fetchall()]

            missing_skills = set(skill_ids) - set(existing_skills)
            if missing_skills:
                raise_http_exception(
                    status_code=400,
                    detail=f"Skills with IDs {list(missing_skills)} do not exist",
                )


def _check_question_availability(skill_ids: List[int], question_selection_mode: str) -> int:
    """Check if there are enough questions available for the selected skills."""
    # question_selection_mode is kept for future use but not currently used in validation
    question_count = count_questions_for_skills(skill_ids)

    if question_count == 0:
        raise_http_exception(
            status_code=400,
            detail="No questions found for the selected skills. Please add questions first.",
        )

    return question_count


def _create_assessment_in_db(
    assessment_name: str, assessment_description: str, question_selection_mode: str, duration: int, skill_ids: list[int]
) -> int:
    """Create assessment in database and return assessment ID."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor() as cur:
            # Create the assessment
            cur.execute(
                """
                INSERT INTO assessments (
                    name, description, is_final, total_questions,
                    question_selection_mode, composition, duration_minutes
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
                """,
                (
                    assessment_name,
                    assessment_description,
                    False,  # is_final is no longer relevant but kept for DB compatibility
                    30,  # Default total_questions
                    question_selection_mode,
                    json.dumps({"basic": 10, "intermediate": 10, "advanced": 10}),
                    duration,
                ),
            )
            assessment_id = cur.fetchone()[0]

            # Create skill associations
            for skill_id in skill_ids:
                cur.execute(
                    """INSERT INTO assessment_skills
                    (assessment_id, skill_id)
                    VALUES (%s, %s) ON CONFLICT DO NOTHING""",
                    (assessment_id, skill_id),
                )

            conn.commit()

    return assessment_id


def _get_assessment_details(assessment_id: int) -> dict:
    """Get assessment details including question selection mode."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                (assessment_id,),
            )
            result = cur.fetchone()
            return dict(result) if result else None


def _validate_assessment_exists(cur, assessment_id: int):
    """Validate that assessment exists in database."""
    cur.execute("SELECT id, name FROM assessments WHERE id = %s", (assessment_id,))
    if not cur.fetchone():
        raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found.")


@assessment_router.post("/admin/quiz")
async def create_quiz(request: CreateQuizRequest, _: None = Depends(rate_limiter)):
    """
    Creates a new assessment based on the provided description and quiz name.

    Args:
        request (CreateQuizRequest): The request body containing:
            topic (str): The user-defined description for the assessment.
            quiz_name (str): The user-defined name for this assessment.
            user_id (str): The ID of the admin creating the quiz.
            skill_ids (list[int]): List of skill IDs to associate with this assessment.

    Returns:
        JSONResponse: A JSON response containing the ID of the created assessment,
        and a success message.
        Admins should then use the /generate_sessions endpoint to create usable session codes.

    Raises:
        HTTPException:
            - 400: If the description is missing or no questions are found.
            - 500: If an error occurs during quiz creation.
    """
    try:
        # Validate request parameters
        _validate_quiz_request(request)

        # Extract request data
        assessment_description = request.topic
        user_defined_assessment_name = request.quiz_name
        user_id = request.user_id
        skill_ids = request.skill_ids
        question_selection_mode = request.question_selection_mode
        duration = request.duration

        # Validate skills
        _validate_skills(skill_ids)

        # Check question availability
        question_count = _check_question_availability(skill_ids, question_selection_mode)
        info(f"Found {question_count} existing questions for the selected skills")

        # Create assessment name with timestamp
        timestamp = datetime.now().strftime("%d_%m_%Y")
        assessment_base_name = f"{user_defined_assessment_name}_{timestamp}"
        assessment_name = f"{assessment_base_name} Assessment"

        # Create assessment in database
        assessment_id = _create_assessment_in_db(
            assessment_name, assessment_description, question_selection_mode, duration, skill_ids
        )

        # Log the assessment creation
        insert_quiz_creation_logs(
            [
                {
                    "user_id": user_id,
                    "assessment_name": assessment_name,
                    "assessment_description": assessment_description,
                    "total_questions": question_count,
                    "assessment_id": assessment_id,
                }
            ]
        )

        return success_response(
            data={
                "assessment_id": assessment_id,
                "assessment_base_name": assessment_base_name,
                "question_selection_mode": question_selection_mode,
                "skill_ids": skill_ids,
                "total_questions_available": question_count,
                "duration": duration,
            },
            message="Assessment created successfully. Please generate sessions to get usable codes.",
        )

    except Exception as e:
        error(f"Error creating quiz: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Error creating assessment: {str(e)}")


@assessment_router.get("/admin/assessments")
async def get_assessments(
    limit: int = Query(7, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    Get all assessments with pagination

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get total count first
                cur.execute("SELECT COUNT(*) FROM assessments")
                total = cur.fetchone()[0]

                # Get paginated results
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes, created_at
                    FROM assessments
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                    """,
                    (limit, offset),
                )
                assessments = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for assessment in assessments:
                    if assessment["created_at"]:
                        assessment["created_at"] = assessment["created_at"].isoformat()

                # Apply hash ID transformation
                hashed_assessments = hash_ids_in_response(assessments)

                return success_response(
                    data={
                        "assessments": hashed_assessments,
                        "pagination": {
                            "total": total,
                            "limit": limit,
                            "offset": offset,
                            "has_more": offset + limit < total,
                        },
                    },
                    message="Assessments retrieved successfully",
                )

    except Exception as e:
        error(f"Error fetching assessments: {str(e)}")
        return error_response(
            message=f"Error fetching assessments: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/assessment/{assessment_id}")
async def get_assessment(
    assessment_id: str,
    include_questions: bool = Query(False),
    include_answers: bool = Query(False),
):
    """
    Get a single assessment without questions by default.
    For security, questions and answers are excluded unless explicitly requested.
    """
    try:
        # Try to decode if it's a hash, otherwise treat as integer
        actual_id = assessment_id
        if not assessment_id.isdigit():
            decoded_id = decode_assessment_id(assessment_id)
            if decoded_id is not None:
                actual_id = decoded_id
            else:
                return error_response(
                    message=f"Invalid assessment ID format: {assessment_id}",
                    code=status.HTTP_400_BAD_REQUEST,
                    error_type="BadRequest",
                )
        else:
            actual_id = int(assessment_id)

        # For security, always exclude questions by default
        # Parameters are kept for API compatibility but not used for security
        safe_include_questions = False  # Always False for security
        safe_include_answers = False  # Always False for security

        # Suppress unused parameter warnings
        _ = include_questions
        _ = include_answers

        # Use the new function from assessment_manager
        assessment_dict = get_assessment_by_id(
            actual_id,
            include_questions=safe_include_questions,
            include_answers=safe_include_answers,
        )

        if not assessment_dict:
            return error_response(
                message=f"Assessment with ID {actual_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Transform response to include hashed IDs
        hashed_assessment = hash_ids_in_response(assessment_dict)

        return success_response(
            data=hashed_assessment,
            message="Assessment retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/assessment-questions/{assessment_id}")
async def get_assessment_questions(assessment_id: int):
    """Get all available questions for an assessment based on its associated skills"""
    try:
        # Use the new function from assessment_manager
        result = get_assessment_questions_by_id(assessment_id)

        if result is None:
            return error_response(
                message=f"Assessment with ID {assessment_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        return success_response(
            data=result,
            message="Assessment questions retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching questions for assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching questions for assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/fixed-assessment-questions/{assessment_id}")
async def get_fixed_assessment_questions(assessment_id: int):
    """Get the assigned questions for a fixed assessment"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists and is a fixed assessment
                cur.execute(
                    "SELECT id, name, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()

                if not assessment:
                    return error_response(
                        message=f"Assessment with ID {assessment_id} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                if assessment["question_selection_mode"] != "fixed":
                    return error_response(
                        message=f"Assessment with ID {assessment_id} is not a fixed assessment",
                        code=status.HTTP_400_BAD_REQUEST,
                        error_type="BadRequest",
                    )

                # Get the assigned questions for this assessment
                cur.execute(
                    "SELECT question_id FROM assessment_questions WHERE assessment_id = %s ORDER BY id",
                    (assessment_id,),
                )
                results = cur.fetchall()

                # Extract question IDs
                question_ids = [row[0] for row in results]

                response_data = {
                    "assessment_id": assessment_id,
                    "assessment_name": assessment["name"],
                    "question_ids": question_ids,
                }

                return success_response(
                    data=response_data,
                    message=f"Retrieved {len(question_ids)} fixed questions for assessment '{assessment['name']}'",
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching fixed assessment questions for assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching fixed assessment questions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/assessments/{assessment_id}")
async def get_single_assessment(assessment_id: int):
    """Get a single assessment by ID for the quiz link page"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes, created_at
                    FROM assessments
                    WHERE id = %s
                    """,
                    (assessment_id,),
                )
                assessment = cur.fetchone()

                if not assessment:
                    raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found")

                return dict(assessment)

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching single assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/assessments-with-sessions")
async def get_assessments_with_sessions():
    """Get only assessments that have existing sessions"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT DISTINCT a.id, a.name, a.description, a.is_final,
                           a.total_questions, a.question_selection_mode,
                           a.composition, a.created_at,
                           COUNT(s.id) as session_count
                    FROM assessments a
                    INNER JOIN sessions s ON a.id = s.assessment_id
                    GROUP BY a.id, a.name, a.description, a.is_final,
                             a.total_questions, a.question_selection_mode,
                             a.composition, a.created_at
                    ORDER BY a.created_at DESC
                    """
                )
                assessments = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for assessment in assessments:
                    if assessment["created_at"]:
                        assessment["created_at"] = assessment["created_at"].isoformat()

                # Apply hash ID transformation
                hashed_assessments = hash_ids_in_response(assessments)

                return success_response(
                    data={"assessments": hashed_assessments},
                    message="Assessments with sessions retrieved successfully",
                )

    except Exception as e:
        error(f"Error fetching assessments with sessions: {str(e)}")
        return error_response(
            message=f"Error fetching assessments with sessions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.post("/admin/reports")
async def generate_report(request: ReportRequest, _: None = Depends(rate_limiter)):
    """Generate reports based on user or topic"""
    try:
        if request.report_type == "user_wise" and request.user_name:
            base_report, score_report = assessment_report_by_user(request.user_name, request.quiz_type or "mock")
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated user-wise report for {request.user_name}",
            )

        elif request.report_type == "topic_wise" and request.report_topic:
            base_report, score_report = assessment_report_by_topic(request.report_topic, request.quiz_type or "mock")
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated topic-wise report for {request.report_topic}",
            )

        elif request.report_type == "assessment_wise" and request.assessment_base_name:
            base_report, score_report = assessment_report_with_question_stats(
                request.assessment_base_name, request.quiz_type or "mock"
            )
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated assessment-wise report for {request.assessment_base_name}",
            )

        else:
            return error_response(
                message="Invalid report type or missing required parameters",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

    except Exception as e:
        error(f"Error generating report: {str(e)}")
        return error_response(
            message=f"Error generating report: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/users/{user_id}/assessments")
async def get_user_assessments(user_id: int):
    """Get all assessments taken by a specific user with their scores"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get user details (excluding sensitive data)
                cur.execute(
                    """
                    SELECT id, external_id, display_name
                    FROM users
                    WHERE id = %s
                    """,
                    (user_id,),
                )
                user = cur.fetchone()

                if not user:
                    return error_response(
                        message=f"User with ID {user_id} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                user_dict = dict(user)
                if not user_dict["display_name"]:
                    user_dict["display_name"] = user_dict["external_id"]

                # Get all sessions for this user with assessment details and scores
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.created_at AS session_created,
                        s.completed_at AS session_completed,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.question_selection_mode AS mode,
                        a.description AS assessment_description,
                        s.score,
                        s.status,

                        COALESCE(SUM(CASE WHEN q.level = 'easy' THEN 1 ELSE 0 END), 0) AS easy_count,
                        COALESCE(SUM(CASE WHEN q.level = 'intermediate' THEN 1 ELSE 0 END), 0) AS intermediate_count,
                        COALESCE(SUM(CASE WHEN q.level = 'advanced' THEN 1 ELSE 0 END), 0) AS advanced_count

                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id
                    LEFT JOIN questions q ON aq.question_id = q.que_id

                    WHERE s.user_id = %s

                    GROUP BY
                        s.id, s.created_at, s.completed_at, s.score, s.status,
                        a.id, a.name, a.question_selection_mode, a.description

                    ORDER BY
                        (s.status = 'completed') DESC,
                        s.created_at DESC;

                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for session in sessions:
                    if session["session_created"]:
                        session["session_created"] = session["session_created"].isoformat()
                    if session["session_completed"]:
                        session["session_completed"] = session["session_completed"].isoformat()

                response_data = {"user": user_dict, "assessments": sessions}
                hashed_data = hash_ids_in_response(response_data)
                return success_response(
                    data=hashed_data,
                    message="User assessments retrieved successfully",
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user assessments: {str(e)}")
        return error_response(
            message=f"Error getting user assessments: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/user/{email}/assessments")
async def get_user_assessments_by_email(email: str):
    """Get all assessments taken by a specific user identified by email"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get user details (excluding sensitive data from response)
                cur.execute(
                    """
                    SELECT id, external_id, display_name
                    FROM users
                    WHERE email = %s
                    """,
                    (email,),
                )
                user = cur.fetchone()

                if not user:
                    return error_response(
                        message=f"User with email {email} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                user_dict = dict(user)
                if not user_dict["display_name"]:
                    user_dict["display_name"] = user_dict["external_id"]

                user_id = user_dict["id"]

                # Get all sessions for this user with assessment details and scores
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.created_at AS session_created,
                        s.completed_at AS session_completed,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.question_selection_mode AS mode,
                        a.description AS assessment_description,
                        s.score,
                        s.status,

                        COALESCE(SUM(CASE WHEN q.level = 'easy' THEN 1 ELSE 0 END), 0) AS easy_count,
                        COALESCE(SUM(CASE WHEN q.level = 'intermediate' THEN 1 ELSE 0 END), 0) AS intermediate_count,
                        COALESCE(SUM(CASE WHEN q.level = 'advanced' THEN 1 ELSE 0 END), 0) AS advanced_count

                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id
                    LEFT JOIN questions q ON aq.question_id = q.que_id

                    WHERE s.user_id = %s

                    GROUP BY
                        s.id, s.created_at, s.completed_at, s.score, s.status,
                        a.id, a.name, a.question_selection_mode, a.description

                    ORDER BY
                        (s.status = 'completed') DESC,
                        s.created_at DESC;

                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for session in sessions:
                    if session["session_created"]:
                        session["session_created"] = session["session_created"].isoformat()
                    if session["session_completed"]:
                        session["session_completed"] = session["session_completed"].isoformat()

                response_data = {"user": user_dict, "assessments": sessions}
                hashed_data = hash_ids_in_response(response_data)
                return success_response(
                    data=hashed_data,
                    message=f"User assessments retrieved successfully for {email}",
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user assessments by email: {str(e)}")
        return error_response(
            message=f"Error getting user assessments by email: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.post("/admin/final-questions")
async def add_final_questions(request: FinalQuestionsRequest, _: None = Depends(rate_limiter)):
    """Add questions to a final assessment or a fixed assessment"""
    try:
        # Check if this is for a fixed assessment
        if request.assessment_id:
            # This is for a fixed assessment
            # First, validate the assessment exists and is a fixed assessment
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                        (request.assessment_id,),
                    )
                    assessment = cur.fetchone()
                    if not assessment:
                        raise_http_exception(
                            status_code=404, detail=f"Assessment with ID {request.assessment_id} not found"
                        )

                    if assessment[1] != "fixed":
                        raise_http_exception(
                            status_code=400,
                            detail=f"Assessment with ID {request.assessment_id} is not a fixed assessment",
                        )

                    # Clear existing questions for this assessment
                    cur.execute("DELETE FROM assessment_questions WHERE assessment_id = %s", (request.assessment_id,))

                    # Add the new questions
                    for question_id in request.question_ids:
                        cur.execute(
                            "INSERT INTO assessment_questions (assessment_id, question_id) VALUES (%s, %s)",
                            (request.assessment_id, question_id),
                        )

                    conn.commit()

            return success_response(
                data={
                    "assessment_id": request.assessment_id,
                    "questions_added": len(request.question_ids),
                },
                message=f"Successfully added {len(request.question_ids)} questions to fixed assessment",
            )

        else:
            # This is for creating a new final assessment (legacy behavior)
            raise_http_exception(
                status_code=400,
                detail="assessment_id is required for adding questions to assessments",
            )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error adding final questions: {str(e)}")
        return error_response(
            message=f"Error adding final questions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )
